import { WORKFLOW_STEPS, OPENROUTER_MODELS, MODEL_DETECTION_CONFIG } from '../constants';
import { modelDetectionService } from './modelDetectionService';
import type { Step, Message } from '../types';

// The API key will be read from process.env inside the function that needs it.
// This is more robust than reading it once at the module level.

const OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions";

// Utilisation du service de détection des modèles

/**
 * Sélectionne intelligemment un modèle pour une tâche donnée
 * Avec rotation automatique et vérification de disponibilité
 */
function selectModelForTask(task: Step['task'], excludeModels: string[] = []): string {
    const modelsForTask = OPENROUTER_MODELS[task];

    // Filtrer les modèles exclus et vérifier la disponibilité via le service
    const availableModels = modelsForTask.filter(model =>
        !excludeModels.includes(model) && modelDetectionService.isModelAvailable(model)
    );

    if (availableModels.length === 0) {
        console.warn(`⚠️ Aucun modèle disponible pour la tâche: ${task}`);
        // Fallback vers le premier modèle de la liste originale
        return modelsForTask[0];
    }

    // Sélection intelligente: varier les modèles pour optimiser le raisonnement agentique
    const selectedModel = availableModels[Math.floor(Math.random() * availableModels.length)];
    console.log(`🤖 Modèle sélectionné pour ${task}: ${selectedModel}`);

    return selectedModel;
}

/**
 * Fonction principale pour envoyer un message à l'IA avec rotation automatique des modèles
 */
export async function sendMessageToAI(
  messages: { role: string; content: string }[],
  task: Step['task']
): Promise<{ content: string; modelUsed: string }> {
    const apiKey = process.env.API_KEY;

    // Vérification robuste de la clé API
    if (!apiKey || apiKey.trim() === '') {
        const errorMsg = "Clé API OpenRouter non trouvée ou vide. Assurez-vous que la variable d'environnement VITE_API_KEY est configurée et accessible par l'application.";
        console.error(errorMsg);
        console.log(`Debug: Valeur de process.env.API_KEY: [${apiKey}]`);
        throw new Error(errorMsg);
    }

    // Mise à jour de la cache des modèles via le service de détection
    await modelDetectionService.updateModelsIfNeeded();

    const excludedModels: string[] = [];
    let lastError: Error | null = null;

    // Tentatives avec rotation des modèles
    for (let attempt = 0; attempt < MODEL_DETECTION_CONFIG.MAX_RETRIES; attempt++) {
        const model = selectModelForTask(task, excludedModels);

        try {
            console.log(`🚀 Tentative ${attempt + 1}/${MODEL_DETECTION_CONFIG.MAX_RETRIES} avec le modèle: ${model}`);

            const response = await fetch(OPENROUTER_API_URL, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json',
                    // Headers recommandés par OpenRouter pour l'accès aux modèles gratuits
                    'HTTP-Referer': 'https://agentic-workflow-studio.web.app',
                    'X-Title': 'Agentic Workflow Studio',
                },
                body: JSON.stringify({
                    model: model,
                    messages: messages,
                    temperature: 0.7,
                    top_p: 0.95,
                }),
            });

            if (!response.ok) {
                const errorBody = await response.json().catch(() => ({
                    error: { message: 'Impossible de lire la réponse d\'erreur de l\'API.' }
                }));

                const errorMsg = `Erreur API OpenRouter: ${response.status} ${response.statusText} - ${errorBody.error?.message || 'Erreur inconnue'}`;
                console.warn(`❌ Échec avec ${model}: ${errorMsg}`);

                // Exclure ce modèle pour les prochaines tentatives
                excludedModels.push(model);
                lastError = new Error(errorMsg);

                // Attendre avant la prochaine tentative
                if (attempt < MODEL_DETECTION_CONFIG.MAX_RETRIES - 1) {
                    await new Promise(resolve => setTimeout(resolve, MODEL_DETECTION_CONFIG.RETRY_DELAY));
                }
                continue;
            }

            const data = await response.json();
            const content = data.choices[0]?.message?.content || "Désolé, je n'ai pas pu générer une réponse.";

            console.log(`✅ Succès avec le modèle: ${model}`);
            return { content, modelUsed: model };

        } catch (error) {
            console.warn(`❌ Erreur avec le modèle ${model}:`, error);
            excludedModels.push(model);
            lastError = error as Error;

            // Attendre avant la prochaine tentative
            if (attempt < MODEL_DETECTION_CONFIG.MAX_RETRIES - 1) {
                await new Promise(resolve => setTimeout(resolve, MODEL_DETECTION_CONFIG.RETRY_DELAY));
            }
        }
    }

    // Si toutes les tentatives ont échoué
    const finalError = lastError || new Error('Tous les modèles ont échoué');
    console.error(`💥 Échec complet après ${MODEL_DETECTION_CONFIG.MAX_RETRIES} tentatives:`, finalError);
    throw finalError;
}

export const generateSystemPrompt = (stepIndex: number, problem: string): string => {
  const step = WORKFLOW_STEPS[stepIndex];
  if (!step) return `Votre tâche est de résumer la conversation à propos de : ${problem}`;

  const commonInstruction = `
Vous êtes un agent IA expert, guidant un utilisateur à travers un workflow de 14 étapes pour résoudre un problème complexe.
Votre personnalité est celle d'un stratège serviable, structuré et créatif.
Le problème principal de l'utilisateur est : "${problem}"

Nous sommes à l'ÉTAPE ${step.id}: **${step.title}**.
Description de cette étape : ${step.description}.
Techniques clés à considérer pour cette étape : ${step.techniques.join(', ')}.
`;

  if (stepIndex === 0) {
    return `
${commonInstruction}
Votre tâche est de commencer l'étape "Définition du Problème". Analysez la description initiale du problème par l'utilisateur.
Posez des questions clarifiantes pour l'aider à définir le problème plus précisément.
Gardez votre réponse concise et terminez en encourageant l'utilisateur à fournir ces détails.
`;
  }

  const nextStep = WORKFLOW_STEPS[stepIndex + 1];
  const finalPrompt = `
${commonInstruction}
Votre tâche est de générer la réponse pour l'étape actuelle (${step.title}).
- Analysez la dernière réponse de l'utilisateur et fournissez une synthèse ou une action pour l'étape actuelle.
- Si vous avez besoin de plus d'informations, posez des questions ciblées.
- Concluez en introduisant la prochaine étape : "${nextStep?.title || 'Génération du Prompt Final'}".
- Restez concis et focalisé sur l'étape en cours.
- Formatez votre réponse en markdown pour plus de clarté.
`;
  return finalPrompt;
};

export const generateFinalOutput = async (conversation: Message[]): Promise<string> => {
    const finalSystemPrompt = `
    Vous êtes un agent IA expert en synthèse finale. Votre tâche est d'analyser l'intégralité d'une conversation de workflow et de produire deux livrables distincts.
    1.  **Le Prompt Optimisé** : Un prompt final, puissant et autonome, prêt à être utilisé dans n'importe quel LLM. Il doit encapsuler l'objectif final et toutes les contraintes et nuances découvertes pendant la conversation.
    2.  **La Méta-Analyse de la Construction** : Une explication sur la manière dont vous avez construit ce prompt, en justifiant vos choix (structure, mots-clés, techniques utilisées) en vous basant sur la conversation.

    Séparez les deux sections par '---'.
    La sortie doit être structurée exactement comme suit:
    
    ### Le Prompt Optimisé
    
    [Votre prompt final ici]
    
    ---
    
    ### Méta-Analyse de la Construction
    
    [Votre analyse ici]
    `;

    const conversationHistory = conversation.map(m => ({
        role: m.sender === 'user' ? 'user' : 'assistant',
        content: m.text
    }));

    const messages = [
        { role: 'system', content: finalSystemPrompt },
        ...conversationHistory
    ];
    
    // This call will now use the robust API key retrieval
    const { content } = await sendMessageToAI(messages, 'synthèse');
    return content;
};
