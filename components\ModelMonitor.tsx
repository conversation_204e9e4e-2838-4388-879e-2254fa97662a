import React, { useState, useEffect } from 'react';
import { modelDetectionService } from '../services/modelDetectionService';

interface ModelStats {
    totalModels: number;
    freeModels: number;
    lastUpdate: number;
    availableByTask: Record<string, string[]>;
}

const ModelIcon: React.FC = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-emerald-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
    </svg>
);

const RefreshIcon: React.FC = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
    </svg>
);

export const ModelMonitor: React.FC<{ className?: string }> = ({ className = '' }) => {
    const [stats, setStats] = useState<ModelStats | null>(null);
    const [isUpdating, setIsUpdating] = useState(false);
    const [lastUpdateText, setLastUpdateText] = useState('');

    const updateStats = async () => {
        const currentStats = modelDetectionService.getModelStats();
        setStats(currentStats);
        
        // Formatage de la date de dernière mise à jour
        if (currentStats.lastUpdate > 0) {
            const date = new Date(currentStats.lastUpdate);
            const now = new Date();
            const diffHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
            
            if (diffHours < 1) {
                setLastUpdateText('À l\'instant');
            } else if (diffHours < 24) {
                setLastUpdateText(`Il y a ${diffHours}h`);
            } else {
                const diffDays = Math.floor(diffHours / 24);
                setLastUpdateText(`Il y a ${diffDays}j`);
            }
        } else {
            setLastUpdateText('Jamais');
        }
    };

    const handleForceUpdate = async () => {
        setIsUpdating(true);
        try {
            await modelDetectionService.forceUpdate();
            await updateStats();
        } catch (error) {
            console.error('Erreur lors de la mise à jour forcée:', error);
        } finally {
            setIsUpdating(false);
        }
    };

    useEffect(() => {
        updateStats();
        
        // Mise à jour périodique des stats
        const interval = setInterval(updateStats, 30000); // Toutes les 30 secondes
        
        return () => clearInterval(interval);
    }, []);

    if (!stats) {
        return (
            <div className={`${className} p-4 bg-slate-800/50 rounded-xl shadow-lg border border-slate-700`}>
                <div className="flex items-center">
                    <ModelIcon />
                    <span className="text-slate-400 text-sm">Chargement des modèles...</span>
                </div>
            </div>
        );
    }

    return (
        <div className={`${className} p-4 bg-slate-800/50 rounded-xl shadow-lg border border-slate-700`}>
            <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-semibold text-emerald-300 flex items-center">
                    <ModelIcon />
                    Modèles IA
                </h3>
                <button
                    onClick={handleForceUpdate}
                    disabled={isUpdating}
                    className={`p-2 rounded-lg transition-colors ${
                        isUpdating 
                            ? 'bg-slate-600 text-slate-400 cursor-not-allowed' 
                            : 'bg-slate-700 text-slate-300 hover:bg-slate-600 hover:text-white'
                    }`}
                    title="Forcer la mise à jour des modèles"
                >
                    <div className={isUpdating ? 'animate-spin' : ''}>
                        <RefreshIcon />
                    </div>
                </button>
            </div>
            
            <div className="space-y-2 text-sm">
                <div className="flex justify-between items-center">
                    <span className="text-slate-400">Modèles gratuits :</span>
                    <span className="text-emerald-400 font-semibold">{stats.freeModels}</span>
                </div>
                
                <div className="flex justify-between items-center">
                    <span className="text-slate-400">Total disponible :</span>
                    <span className="text-slate-300">{stats.totalModels}</span>
                </div>
                
                <div className="flex justify-between items-center">
                    <span className="text-slate-400">Dernière MAJ :</span>
                    <span className="text-slate-300">{lastUpdateText}</span>
                </div>
            </div>
            
            <div className="mt-4 pt-3 border-t border-slate-700">
                <h4 className="text-sm font-medium text-slate-300 mb-2">Modèles par tâche :</h4>
                <div className="space-y-1 text-xs">
                    {Object.entries(stats.availableByTask).map(([task, models]) => (
                        <div key={task} className="flex justify-between items-center">
                            <span className="text-slate-400 capitalize">{task} :</span>
                            <span className="text-emerald-400">{models.length}</span>
                        </div>
                    ))}
                </div>
            </div>
            
            {stats.freeModels === 0 && (
                <div className="mt-3 p-2 bg-amber-900/20 border border-amber-700/50 rounded-lg">
                    <p className="text-amber-400 text-xs">
                        ⚠️ Aucun modèle gratuit détecté. Vérifiez votre connexion.
                    </p>
                </div>
            )}
        </div>
    );
};
