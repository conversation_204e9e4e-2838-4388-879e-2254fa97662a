import React, { useState } from 'react';

interface UserGuidanceProps {
  currentStep: number;
  totalSteps: number;
  isProcessing: boolean;
}

const STEP_GUIDANCE = {
  0: {
    title: "🎯 Définition du Problème",
    tips: [
      "Soyez précis dans votre description",
      "Mentionnez le contexte et les contraintes",
      "Indiquez vos objectifs finaux"
    ]
  },
  1: {
    title: "🔍 Analyse Approfondie", 
    tips: [
      "L'agent analyse votre problème en détail",
      "Il identifie les enjeux clés",
      "Préparez-vous à préciser certains points"
    ]
  },
  2: {
    title: "💡 Génération d'Idées",
    tips: [
      "Plusieurs approches vont être proposées",
      "N'hésitez pas à exprimer vos préférences",
      "Demandez des clarifications si nécessaire"
    ]
  },
  3: {
    title: "⚖️ Évaluation des Solutions",
    tips: [
      "Les solutions sont comparées et évaluées",
      "Vous pouvez influencer les critères d'évaluation",
      "Exprimez vos priorités et contraintes"
    ]
  },
  default: {
    title: "🚀 Progression du Workflow",
    tips: [
      "Restez engagé dans le processus",
      "Vos retours améliorent la solution finale",
      "N'hésitez pas à poser des questions"
    ]
  }
};

export const UserGuidance: React.FC<UserGuidanceProps> = ({ 
  currentStep, 
  totalSteps, 
  isProcessing 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const guidance = STEP_GUIDANCE[currentStep as keyof typeof STEP_GUIDANCE] || STEP_GUIDANCE.default;
  const progress = Math.round((currentStep / totalSteps) * 100);

  return (
    <div className="bg-gradient-to-r from-blue-900/20 to-indigo-900/20 rounded-lg border border-blue-500/30 overflow-hidden">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full p-3 flex items-center justify-between hover:bg-blue-900/10 transition-colors"
      >
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="text-left">
            <div className="text-sm font-medium text-blue-300">{guidance.title}</div>
            <div className="text-xs text-blue-400">Étape {currentStep + 1}/{totalSteps} • {progress}% complété</div>
          </div>
        </div>
        
        <svg 
          className={`w-4 h-4 text-blue-400 transition-transform ${isExpanded ? 'rotate-180' : ''}`} 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>
      
      {isExpanded && (
        <div className="px-3 pb-3">
          <div className="bg-blue-900/10 rounded p-3 border border-blue-500/20">
            <h4 className="text-sm font-medium text-blue-300 mb-2">💡 Conseils pour cette étape :</h4>
            <ul className="space-y-1">
              {guidance.tips.map((tip, index) => (
                <li key={index} className="text-xs text-blue-200 flex items-start gap-2">
                  <span className="text-blue-400 mt-0.5">•</span>
                  <span>{tip}</span>
                </li>
              ))}
            </ul>
            
            {isProcessing && (
              <div className="mt-3 pt-3 border-t border-blue-500/20">
                <div className="flex items-center gap-2 text-xs text-blue-300">
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                  L'agent traite votre demande...
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
