import React from 'react';

interface EngagementPromptsProps {
  currentStep: number;
  totalSteps: number;
  onSuggestedAction?: (action: string) => void;
}

const ENGAGEMENT_SUGGESTIONS = {
  early: [
    "Voulez-vous que je détaille davantage un aspect particulier ?",
    "Y a-t-il des contraintes spécifiques que nous devrions considérer ?",
    "Souhaitez-vous explorer une approche alternative ?"
  ],
  middle: [
    "Cette direction vous semble-t-elle pertinente ?",
    "Faut-il ajuster notre approche selon vos retours ?",
    "Y a-t-il des éléments à approfondir ou à modifier ?"
  ],
  late: [
    "Cette solution répond-elle à vos attentes ?",
    "Voulez-vous que nous affinions certains aspects ?",
    "Êtes-vous prêt pour la synthèse finale ?"
  ]
};

export const EngagementPrompts: React.FC<EngagementPromptsProps> = ({ 
  currentStep, 
  totalSteps, 
  onSuggestedAction 
}) => {
  const getPhase = () => {
    const progress = currentStep / totalSteps;
    if (progress < 0.33) return 'early';
    if (progress < 0.66) return 'middle';
    return 'late';
  };

  const getSuggestions = () => {
    const phase = getPhase();
    return ENGAGEMENT_SUGGESTIONS[phase];
  };

  const handleSuggestionClick = (suggestion: string) => {
    if (onSuggestedAction) {
      onSuggestedAction(suggestion);
    }
  };

  return (
    <div className="mt-4 p-4 bg-slate-800/30 rounded-lg border border-slate-600/50">
      <div className="flex items-center gap-2 mb-3">
        <div className="w-2 h-2 bg-indigo-400 rounded-full animate-pulse"></div>
        <span className="text-sm text-slate-300 font-medium">Suggestions d'interaction</span>
      </div>
      
      <div className="space-y-2">
        {getSuggestions().map((suggestion, index) => (
          <button
            key={index}
            onClick={() => handleSuggestionClick(suggestion)}
            className="w-full text-left text-sm text-slate-400 hover:text-slate-200 hover:bg-slate-700/50 p-2 rounded transition-all duration-200 border border-transparent hover:border-slate-600"
          >
            💡 {suggestion}
          </button>
        ))}
      </div>
      
      <div className="mt-3 pt-3 border-t border-slate-700">
        <p className="text-xs text-slate-500 text-center">
          Cliquez sur une suggestion ou tapez votre propre message
        </p>
      </div>
    </div>
  );
};
